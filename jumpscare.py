import cv2
import pygame
import time
import os

# Init Pygame
pygame.init()
screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
pygame.mouse.set_visible(False)

# Get screen resolution
screen_w = pygame.display.Info().current_w
screen_h = pygame.display.Info().current_h

# Function to play video once
def play_video(path):
    cap = cv2.VideoCapture(path)
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        frame = cv2.resize(frame, (screen_w, screen_h))
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        surface = pygame.surfarray.make_surface(frame.swapaxes(0, 1))
        screen.blit(surface, (0, 0))
        pygame.display.update()

        for event in pygame.event.get():
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                cap.release()
                pygame.quit()
                exit()

    cap.release()

# 🔁 Loop 2 times
for _ in range(2):
    play_video("scare.mp4")

# 👋 End it after second play
pygame.quit()
