class Node:
    def __init__(self, value):
        self.value = value
        self.children = []
        
from collections import deque

def bfs(root,goal):
    if not root:
        return
    
    queue = deque([root])

    while queue:
        current_node = queue.popleft()
        print(f"Visiting: {current_node.value}")

        if current_node.value == goal:
            print(f"Goal node '{goal}' found!")
            return current_node

        for child in current_node.children:
            queue.append(child)

    print(f"Goal node: '{goal}' not found in the tree")
    return None  

A = Node('A')
B = Node('B')
C = Node('C')
D = Node('D')
E = Node('E')

       
A.children = [B,C,D]
B.children = [E]
D.children = []            

    
bfs(A,'F')
