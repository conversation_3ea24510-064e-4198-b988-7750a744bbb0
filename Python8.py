class Node:
    def __init__(self, value):
        self.value = value
        self.children = []
        
from collections import deque

def bfs(root):
    if not root:
        return
    
    queue = deque([root])

    while queue:
        current_node = queue.popleft()
        print(current_node.value, end=" ")

        for child in current_node.children:
            queue.append(child)
A = Node('A')
B = Node('B')
C = Node('C')
D = Node('D')
E = Node('E')
F = Node('F')
       
A.children = [B,C,D]
B.children = [E]
D.children = [F]
print("Breadth First Traversal:")
bfs(A)

